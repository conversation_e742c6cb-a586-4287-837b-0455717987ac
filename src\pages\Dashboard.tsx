import { useEffect } from "react";
import {
  CalendarIcon,
  UsersIcon,
  QrCodeIcon,
  TrendingUpIcon,
} from "lucide-react";
import { useEventStore } from "../store/eventStore";

const Dashboard = () => {
  const { events, attendees, initializeMockData, getEventAttendees } =
    useEventStore();

  useEffect(() => {
    // Initialize with mock data if no events exist
    if (events.length === 0) {
      initializeMockData();
    }
  }, [events.length, initializeMockData]);

  // Calculate statistics from real data
  const totalAttendees = attendees.length;
  const totalCheckedIn = attendees.filter((a) => a.checkedIn).length;
  const checkInRate =
    totalAttendees > 0 ? (totalCheckedIn / totalAttendees) * 100 : 0;

  const stats = [
    {
      name: "Total Events",
      value: events.length.toString(),
      icon: CalendarIcon,
      change: "+2.1%",
      changeType: "positive" as const,
    },
    {
      name: "Total Attendees",
      value: totalAttendees.toLocaleString(),
      icon: UsersIcon,
      change: "+15.3%",
      changeType: "positive" as const,
    },
    {
      name: "QR Codes Scanned",
      value: totalCheckedIn.toLocaleString(),
      icon: QrCodeIcon,
      change: "+8.2%",
      changeType: "positive" as const,
    },
    {
      name: "Check-in Rate",
      value: `${checkInRate.toFixed(1)}%`,
      icon: TrendingUpIcon,
      change: "+3.1%",
      changeType: "positive" as const,
    },
  ];

  // Get recent events with real attendee data
  const recentEvents = events.slice(0, 3).map((event) => {
    const eventAttendees = getEventAttendees(event.id);
    const checkedInCount = eventAttendees.filter((a) => a.checkedIn).length;

    return {
      id: event.id,
      name: event.name,
      date: event.startDate.toISOString().split("T")[0],
      attendees: eventAttendees.length,
      checkedIn: checkedInCount,
      status:
        event.status === "published"
          ? "active"
          : event.status === "completed"
          ? "completed"
          : "upcoming",
    };
  });

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-2xl font-bold text-gray-900">Dashboard</h1>
        <p className="mt-1 text-sm text-gray-500">
          Welcome back! Here's what's happening with your events.
        </p>
      </div>

      {/* Stats Grid */}
      <div className="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4">
        {stats.map((stat) => {
          const Icon = stat.icon;
          return (
            <div
              key={stat.name}
              className="relative bg-white pt-5 px-4 pb-12 sm:pt-6 sm:px-6 shadow rounded-lg overflow-hidden"
            >
              <dt>
                <div className="absolute bg-blue-500 rounded-md p-3">
                  <Icon className="h-6 w-6 text-white" />
                </div>
                <p className="ml-16 text-sm font-medium text-gray-500 truncate">
                  {stat.name}
                </p>
              </dt>
              <dd className="ml-16 pb-6 flex items-baseline sm:pb-7">
                <p className="text-2xl font-semibold text-gray-900">
                  {stat.value}
                </p>
                <p
                  className={`ml-2 flex items-baseline text-sm font-semibold ${
                    stat.changeType === "positive"
                      ? "text-green-600"
                      : "text-red-600"
                  }`}
                >
                  {stat.change}
                </p>
              </dd>
            </div>
          );
        })}
      </div>

      {/* Recent Events */}
      <div className="bg-white shadow rounded-lg">
        <div className="px-4 py-5 sm:p-6">
          <h3 className="text-lg leading-6 font-medium text-gray-900">
            Recent Events
          </h3>
          <div className="mt-6 flow-root">
            <ul className="-my-5 divide-y divide-gray-200">
              {recentEvents.map((event) => (
                <li key={event.id} className="py-4">
                  <div className="flex items-center space-x-4">
                    <div className="flex-1 min-w-0">
                      <p className="text-sm font-medium text-gray-900 truncate">
                        {event.name}
                      </p>
                      <p className="text-sm text-gray-500">
                        {new Date(event.date).toLocaleDateString()} •{" "}
                        {event.attendees} attendees
                      </p>
                    </div>
                    <div className="flex items-center space-x-2">
                      <span
                        className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                          event.status === "active"
                            ? "bg-green-100 text-green-800"
                            : event.status === "completed"
                            ? "bg-gray-100 text-gray-800"
                            : "bg-blue-100 text-blue-800"
                        }`}
                      >
                        {event.status}
                      </span>
                      <div className="text-sm text-gray-500">
                        {event.checkedIn}/{event.attendees} checked in
                      </div>
                    </div>
                  </div>
                </li>
              ))}
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Dashboard;
