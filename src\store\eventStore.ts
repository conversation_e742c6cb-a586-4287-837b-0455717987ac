import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { Event, Attendee, EventFormData, AttendeeFormData } from '../types';

interface EventState {
  events: Event[];
  attendees: Attendee[];
  selectedEvent: Event | null;
  isLoading: boolean;
  
  // Event actions
  createEvent: (eventData: EventFormData) => Promise<Event>;
  updateEvent: (id: string, eventData: Partial<EventFormData>) => Promise<Event>;
  deleteEvent: (id: string) => Promise<void>;
  getEvent: (id: string) => Event | undefined;
  setSelectedEvent: (event: Event | null) => void;
  
  // Attendee actions
  addAttendee: (eventId: string, attendeeData: AttendeeFormData) => Promise<Attendee>;
  removeAttendee: (attendeeId: string) => Promise<void>;
  getEventAttendees: (eventId: string) => Attendee[];
  checkInAttendee: (attendeeId: string) => Promise<void>;
  
  // Utility actions
  setLoading: (loading: boolean) => void;
  initializeMockData: () => void;
}

// Mock data generator
const generateMockEvents = (): Event[] => [
  {
    id: '1',
    name: 'Tech Conference 2024',
    description: 'Annual technology conference featuring the latest innovations in software development, AI, and cloud computing.',
    location: 'Convention Center, Downtown',
    startDate: new Date('2024-07-15T09:00:00'),
    endDate: new Date('2024-07-15T17:00:00'),
    status: 'published',
    organizerId: '1',
    maxAttendees: 500,
    createdAt: new Date('2024-06-01T10:00:00'),
    updatedAt: new Date('2024-06-15T14:30:00'),
  },
  {
    id: '2',
    name: 'Marketing Workshop',
    description: 'Interactive workshop on digital marketing strategies and social media engagement.',
    location: 'Business Center, Room 201',
    startDate: new Date('2024-07-20T14:00:00'),
    endDate: new Date('2024-07-20T18:00:00'),
    status: 'completed',
    organizerId: '1',
    maxAttendees: 50,
    createdAt: new Date('2024-06-10T09:00:00'),
    updatedAt: new Date('2024-07-21T10:00:00'),
  },
  {
    id: '3',
    name: 'Product Launch Event',
    description: 'Exclusive launch event for our new product line with live demonstrations and networking.',
    location: 'Grand Hotel Ballroom',
    startDate: new Date('2024-08-05T19:00:00'),
    endDate: new Date('2024-08-05T22:00:00'),
    status: 'draft',
    organizerId: '1',
    maxAttendees: 200,
    createdAt: new Date('2024-06-20T11:00:00'),
    updatedAt: new Date('2024-06-25T16:00:00'),
  },
];

const generateMockAttendees = (): Attendee[] => [
  {
    id: '1',
    eventId: '1',
    email: '<EMAIL>',
    name: 'John Doe',
    qrCode: 'QR_1_TECH_CONF_2024',
    registeredAt: new Date('2024-06-05T10:30:00'),
    checkedIn: true,
    checkedInAt: new Date('2024-07-15T08:45:00'),
  },
  {
    id: '2',
    eventId: '1',
    email: '<EMAIL>',
    name: 'Jane Smith',
    qrCode: 'QR_2_TECH_CONF_2024',
    registeredAt: new Date('2024-06-08T14:20:00'),
    checkedIn: false,
  },
  {
    id: '3',
    eventId: '2',
    email: '<EMAIL>',
    name: 'Mike Johnson',
    qrCode: 'QR_3_MARKETING_WS_2024',
    registeredAt: new Date('2024-06-12T09:15:00'),
    checkedIn: true,
    checkedInAt: new Date('2024-07-20T13:55:00'),
  },
];

export const useEventStore = create<EventState>()(
  persist(
    (set, get) => ({
      events: [],
      attendees: [],
      selectedEvent: null,
      isLoading: false,

      createEvent: async (eventData: EventFormData) => {
        set({ isLoading: true });
        
        // Simulate API delay
        await new Promise(resolve => setTimeout(resolve, 500));
        
        const newEvent: Event = {
          id: Date.now().toString(),
          ...eventData,
          startDate: new Date(eventData.startDate),
          endDate: new Date(eventData.endDate),
          status: 'draft',
          organizerId: '1', // Current user ID
          createdAt: new Date(),
          updatedAt: new Date(),
        };

        set(state => ({
          events: [...state.events, newEvent],
          isLoading: false,
        }));

        return newEvent;
      },

      updateEvent: async (id: string, eventData: Partial<EventFormData>) => {
        set({ isLoading: true });
        
        await new Promise(resolve => setTimeout(resolve, 300));
        
        set(state => ({
          events: state.events.map(event =>
            event.id === id
              ? {
                  ...event,
                  ...eventData,
                  startDate: eventData.startDate ? new Date(eventData.startDate) : event.startDate,
                  endDate: eventData.endDate ? new Date(eventData.endDate) : event.endDate,
                  updatedAt: new Date(),
                }
              : event
          ),
          isLoading: false,
        }));

        const updatedEvent = get().events.find(e => e.id === id);
        return updatedEvent!;
      },

      deleteEvent: async (id: string) => {
        set({ isLoading: true });
        
        await new Promise(resolve => setTimeout(resolve, 300));
        
        set(state => ({
          events: state.events.filter(event => event.id !== id),
          attendees: state.attendees.filter(attendee => attendee.eventId !== id),
          selectedEvent: state.selectedEvent?.id === id ? null : state.selectedEvent,
          isLoading: false,
        }));
      },

      getEvent: (id: string) => {
        return get().events.find(event => event.id === id);
      },

      setSelectedEvent: (event: Event | null) => {
        set({ selectedEvent: event });
      },

      addAttendee: async (eventId: string, attendeeData: AttendeeFormData) => {
        set({ isLoading: true });
        
        await new Promise(resolve => setTimeout(resolve, 300));
        
        const newAttendee: Attendee = {
          id: Date.now().toString(),
          eventId,
          ...attendeeData,
          qrCode: `QR_${Date.now()}_${eventId}`,
          registeredAt: new Date(),
          checkedIn: false,
        };

        set(state => ({
          attendees: [...state.attendees, newAttendee],
          isLoading: false,
        }));

        return newAttendee;
      },

      removeAttendee: async (attendeeId: string) => {
        set({ isLoading: true });
        
        await new Promise(resolve => setTimeout(resolve, 200));
        
        set(state => ({
          attendees: state.attendees.filter(attendee => attendee.id !== attendeeId),
          isLoading: false,
        }));
      },

      getEventAttendees: (eventId: string) => {
        return get().attendees.filter(attendee => attendee.eventId === eventId);
      },

      checkInAttendee: async (attendeeId: string) => {
        set({ isLoading: true });
        
        await new Promise(resolve => setTimeout(resolve, 200));
        
        set(state => ({
          attendees: state.attendees.map(attendee =>
            attendee.id === attendeeId
              ? {
                  ...attendee,
                  checkedIn: true,
                  checkedInAt: new Date(),
                }
              : attendee
          ),
          isLoading: false,
        }));
      },

      setLoading: (loading: boolean) => {
        set({ isLoading: loading });
      },

      initializeMockData: () => {
        const events = generateMockEvents();
        const attendees = generateMockAttendees();
        set({ events, attendees });
      },
    }),
    {
      name: 'event-storage',
    }
  )
);
