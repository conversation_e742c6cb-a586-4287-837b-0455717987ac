// User types
export interface User {
  id: string;
  email: string;
  name: string;
  role: 'organizer' | 'admin';
  createdAt: Date;
  updatedAt: Date;
}

// Event types
export interface Event {
  id: string;
  name: string;
  description: string;
  location: string;
  startDate: Date;
  endDate: Date;
  status: 'draft' | 'published' | 'completed' | 'cancelled';
  organizerId: string;
  maxAttendees?: number;
  createdAt: Date;
  updatedAt: Date;
}

// Attendee types
export interface Attendee {
  id: string;
  eventId: string;
  email: string;
  name: string;
  qrCode: string;
  registeredAt: Date;
  checkedIn: boolean;
  checkedInAt?: Date;
}

// Check-in types
export interface CheckIn {
  id: string;
  attendeeId: string;
  eventId: string;
  checkedInAt: Date;
  checkedInBy: string; // User ID who performed the check-in
}

// QR Code data structure
export interface QRCodeData {
  attendeeId: string;
  eventId: string;
  timestamp: number;
  signature: string; // For security/validation
}

// API Response types
export interface ApiResponse<T> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

// Form types
export interface EventFormData {
  name: string;
  description: string;
  location: string;
  startDate: string;
  endDate: string;
  maxAttendees?: number;
}

export interface AttendeeFormData {
  name: string;
  email: string;
}

// Statistics types
export interface EventStats {
  totalAttendees: number;
  checkedInCount: number;
  checkInRate: number;
  hourlyCheckIns: { hour: string; count: number }[];
}

// Auth types
export interface AuthUser {
  id: string;
  email: string;
  name: string;
  role: 'organizer' | 'admin';
}

export interface LoginCredentials {
  email: string;
  password: string;
}

export interface RegisterData {
  name: string;
  email: string;
  password: string;
  role: 'organizer' | 'admin';
}
