import { useState, useEffect } from "react";
import {
  PlusIcon,
  CalendarIcon,
  MapPinIcon,
  UsersIcon,
  EditIcon,
  TrashIcon,
  EyeIcon,
  MoreVerticalIcon,
} from "lucide-react";
import EventModal from "../components/EventModal";
import EventDetailsModal from "../components/EventDetailsModal";
import { useEventStore } from "../store/eventStore";
import type { Event } from "../types";

const EventsPage = () => {
  const {
    events,
    isLoading,
    deleteEvent,
    setSelectedEvent,
    initializeMockData,
    getEventAttendees,
  } = useEventStore();

  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [isDetailsModalOpen, setIsDetailsModalOpen] = useState(false);
  const [selectedEvent, setSelectedEventLocal] = useState<Event | null>(null);
  const [dropdownOpen, setDropdownOpen] = useState<string | null>(null);

  useEffect(() => {
    // Initialize with mock data if no events exist
    if (events.length === 0) {
      initializeMockData();
    }
  }, [events.length, initializeMockData]);

  const handleEditEvent = (event: Event) => {
    setSelectedEventLocal(event);
    setSelectedEvent(event);
    setIsEditModalOpen(true);
    setDropdownOpen(null);
  };

  const handleViewEvent = (event: Event) => {
    setSelectedEventLocal(event);
    setSelectedEvent(event);
    setIsDetailsModalOpen(true);
    setDropdownOpen(null);
  };

  const handleDeleteEvent = async (eventId: string) => {
    if (
      window.confirm(
        "Are you sure you want to delete this event? This action cannot be undone."
      )
    ) {
      await deleteEvent(eventId);
    }
    setDropdownOpen(null);
  };

  const getStatusColor = (status: Event["status"]) => {
    switch (status) {
      case "published":
        return "bg-green-100 text-green-800";
      case "draft":
        return "bg-gray-100 text-gray-800";
      case "completed":
        return "bg-blue-100 text-blue-800";
      case "cancelled":
        return "bg-red-100 text-red-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  const formatDate = (date: Date) => {
    return new Intl.DateTimeFormat("en-US", {
      month: "short",
      day: "numeric",
      year: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    }).format(date);
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Events</h1>
          <p className="mt-1 text-sm text-gray-500">
            Manage your events and track attendance.
          </p>
        </div>
        <button
          onClick={() => setIsCreateModalOpen(true)}
          className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
        >
          <PlusIcon className="h-4 w-4 mr-2" />
          Create Event
        </button>
      </div>

      {/* Events Grid */}
      {isLoading ? (
        <div className="text-center py-12">
          <div className="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
          <p className="mt-2 text-sm text-gray-500">Loading events...</p>
        </div>
      ) : events.length === 0 ? (
        <div className="bg-white shadow rounded-lg p-6">
          <div className="text-center py-12">
            <CalendarIcon className="h-16 w-16 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              No events yet
            </h3>
            <p className="text-gray-500 mb-4">
              Get started by creating your first event.
            </p>
            <button
              onClick={() => setIsCreateModalOpen(true)}
              className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700"
            >
              <PlusIcon className="h-4 w-4 mr-2" />
              Create Event
            </button>
          </div>
        </div>
      ) : (
        <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3">
          {events.map((event) => {
            const attendees = getEventAttendees(event.id);
            const checkedInCount = attendees.filter((a) => a.checkedIn).length;

            return (
              <div
                key={event.id}
                className="bg-white overflow-hidden shadow rounded-lg"
              >
                <div className="p-6">
                  <div className="flex items-center justify-between">
                    <div className="flex-1 min-w-0">
                      <h3 className="text-lg font-medium text-gray-900 truncate">
                        {event.name}
                      </h3>
                      <p className="mt-1 text-sm text-gray-500 line-clamp-2">
                        {event.description}
                      </p>
                    </div>
                    <div className="relative ml-4">
                      <button
                        onClick={() =>
                          setDropdownOpen(
                            dropdownOpen === event.id ? null : event.id
                          )
                        }
                        className="p-2 rounded-full hover:bg-gray-100"
                      >
                        <MoreVerticalIcon className="h-4 w-4 text-gray-400" />
                      </button>

                      {dropdownOpen === event.id && (
                        <div className="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg z-10 border">
                          <div className="py-1">
                            <button
                              onClick={() => handleViewEvent(event)}
                              className="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 w-full text-left"
                            >
                              <EyeIcon className="h-4 w-4 mr-2" />
                              View Details
                            </button>
                            <button
                              onClick={() => handleEditEvent(event)}
                              className="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 w-full text-left"
                            >
                              <EditIcon className="h-4 w-4 mr-2" />
                              Edit Event
                            </button>
                            <button
                              onClick={() => handleDeleteEvent(event.id)}
                              className="flex items-center px-4 py-2 text-sm text-red-600 hover:bg-red-50 w-full text-left"
                            >
                              <TrashIcon className="h-4 w-4 mr-2" />
                              Delete Event
                            </button>
                          </div>
                        </div>
                      )}
                    </div>
                  </div>

                  <div className="mt-4 space-y-2">
                    <div className="flex items-center text-sm text-gray-500">
                      <CalendarIcon className="h-4 w-4 mr-2" />
                      {formatDate(event.startDate)}
                    </div>
                    <div className="flex items-center text-sm text-gray-500">
                      <MapPinIcon className="h-4 w-4 mr-2" />
                      {event.location}
                    </div>
                    <div className="flex items-center text-sm text-gray-500">
                      <UsersIcon className="h-4 w-4 mr-2" />
                      {attendees.length} registered
                      {event.maxAttendees && ` / ${event.maxAttendees} max`}
                    </div>
                  </div>

                  <div className="mt-4 flex items-center justify-between">
                    <span
                      className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(
                        event.status
                      )}`}
                    >
                      {event.status}
                    </span>
                    <div className="text-sm text-gray-500">
                      {checkedInCount}/{attendees.length} checked in
                    </div>
                  </div>
                </div>
              </div>
            );
          })}
        </div>
      )}

      {/* Modals */}
      <EventModal
        isOpen={isCreateModalOpen}
        onClose={() => setIsCreateModalOpen(false)}
        mode="create"
      />

      <EventModal
        isOpen={isEditModalOpen}
        onClose={() => {
          setIsEditModalOpen(false);
          setSelectedEventLocal(null);
          setSelectedEvent(null);
        }}
        mode="edit"
        event={selectedEvent}
      />

      <EventDetailsModal
        isOpen={isDetailsModalOpen}
        onClose={() => {
          setIsDetailsModalOpen(false);
          setSelectedEventLocal(null);
          setSelectedEvent(null);
        }}
        event={selectedEvent}
      />
    </div>
  );
};

export default EventsPage;
